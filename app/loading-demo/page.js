"use client";

import LoadingSpinner from "@/components/LoadingSpinner";

export default function LoadingDemo() {
  const variants = [
    { name: "Pulse", variant: "pulse" },
    { name: "Spin", variant: "spin" },
    { name: "Dots", variant: "dots" },
    { name: "Orbit", variant: "orbit" },
    { name: "Ripple", variant: "ripple" },
    { name: "Bars", variant: "bars" },
    { name: "Gradient", variant: "gradient" },
  ];

  const sizes = ["xs", "sm", "md", "lg", "xl"];
  const colors = ["primary", "secondary", "accent", "success", "warning", "error"];

  return (
    <div className="min-h-screen bg-base-100 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-base-content mb-4">
            Loading Spinner Showcase
          </h1>
          <p className="text-base-content/70">
            Beautiful, customizable loading animations for your application
          </p>
        </div>

        {/* Variants Section */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            Animation Variants
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {variants.map((item) => (
              <div key={item.variant} className="text-center">
                <div className="bg-base-200 rounded-lg p-8 mb-4 flex items-center justify-center min-h-[120px]">
                  <LoadingSpinner variant={item.variant} size="lg" />
                </div>
                <h3 className="font-medium text-base-content">{item.name}</h3>
                <code className="text-xs text-base-content/60">variant="{item.variant}"</code>
              </div>
            ))}
          </div>
        </section>

        {/* Sizes Section */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            Size Options
          </h2>
          <div className="bg-base-200 rounded-lg p-8">
            <div className="flex items-center justify-center space-x-8 flex-wrap gap-4">
              {sizes.map((size) => (
                <div key={size} className="text-center">
                  <LoadingSpinner variant="gradient" size={size} />
                  <p className="text-xs text-base-content/60 mt-2">{size}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Colors Section */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            Color Options
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {colors.map((color) => (
              <div key={color} className="text-center">
                <div className="bg-base-200 rounded-lg p-6 mb-2 flex items-center justify-center">
                  <LoadingSpinner variant="spin" size="md" color={color} />
                </div>
                <p className="text-xs text-base-content/60 capitalize">{color}</p>
              </div>
            ))}
          </div>
        </section>

        {/* With Text Examples */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            With Text Examples
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-base-200 rounded-lg p-8 text-center">
              <LoadingSpinner 
                variant="pulse" 
                size="lg" 
                color="primary" 
                text="Loading..." 
              />
            </div>
            <div className="bg-base-200 rounded-lg p-8 text-center">
              <LoadingSpinner 
                variant="dots" 
                size="md" 
                color="success" 
                text="Processing payment..." 
              />
            </div>
            <div className="bg-base-200 rounded-lg p-8 text-center">
              <LoadingSpinner 
                variant="gradient" 
                size="lg" 
                text="Redirecting..." 
              />
            </div>
          </div>
        </section>

        {/* Usage Examples */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            Usage Examples
          </h2>
          <div className="bg-base-300 rounded-lg p-6">
            <pre className="text-sm text-base-content overflow-x-auto">
{`// Basic usage
<LoadingSpinner />

// With custom variant and size
<LoadingSpinner variant="gradient" size="lg" />

// With text and color
<LoadingSpinner 
  variant="pulse" 
  size="md" 
  color="primary" 
  text="Loading..." 
/>

// All available props
<LoadingSpinner 
  size="xs|sm|md|lg|xl"
  variant="pulse|spin|dots|orbit|ripple|bars|gradient"
  color="primary|secondary|accent|neutral|info|success|warning|error|white|gray"
  text="Optional loading text"
  className="additional-classes"
/>`}
            </pre>
          </div>
        </section>

        {/* Real-world Examples */}
        <section>
          <h2 className="text-2xl font-semibold text-base-content mb-8 text-center">
            Real-world Examples
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Button Loading State */}
            <div className="bg-base-200 rounded-lg p-6">
              <h3 className="font-medium text-base-content mb-4">Button Loading State</h3>
              <button className="btn btn-primary" disabled>
                <LoadingSpinner variant="spin" size="sm" color="white" />
                Processing...
              </button>
            </div>

            {/* Card Loading State */}
            <div className="bg-base-200 rounded-lg p-6">
              <h3 className="font-medium text-base-content mb-4">Card Loading State</h3>
              <div className="card bg-base-100 shadow-xl">
                <div className="card-body items-center text-center">
                  <LoadingSpinner variant="dots" size="lg" color="primary" text="Loading content..." />
                </div>
              </div>
            </div>

            {/* Full Page Loading */}
            <div className="bg-base-200 rounded-lg p-6">
              <h3 className="font-medium text-base-content mb-4">Full Page Loading</h3>
              <div className="bg-base-100 rounded border-2 border-dashed border-base-300 h-32 flex items-center justify-center">
                <LoadingSpinner variant="gradient" size="xl" text="Loading application..." />
              </div>
            </div>

            {/* Inline Loading */}
            <div className="bg-base-200 rounded-lg p-6">
              <h3 className="font-medium text-base-content mb-4">Inline Loading</h3>
              <p className="text-base-content">
                Fetching data 
                <LoadingSpinner variant="dots" size="xs" color="gray" className="inline-block ml-2" />
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
