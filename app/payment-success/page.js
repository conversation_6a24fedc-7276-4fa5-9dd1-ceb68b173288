"use client";

import { useState, useEffect } from "react";
import { useSession, getProviders, signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import config from "@/config";
import LoadingSpinner from "@/components/LoadingSpinner";

export default function PaymentSuccess() {
  const { data: session, status } = useSession();
  const [providers, setProviders] = useState(null);
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState({
    isProcessed: false,
    isChecking: false,
    attempts: 0,
    maxAttempts: 30, // 30 attempts = 5 minutes with 10-second intervals
  });
  const router = useRouter();

  useEffect(() => {
    const setUpProviders = async () => {
      const response = await getProviders();
      setProviders(response);
    };
    setUpProviders();
  }, []);

  // Poll payment status for authenticated users
  useEffect(() => {
    if (session && !paymentStatus.isProcessed) {
      const checkPaymentStatus = async () => {
        if (paymentStatus.isChecking || paymentStatus.attempts >= paymentStatus.maxAttempts) {
          return;
        }

        setPaymentStatus(prev => ({ ...prev, isChecking: true, attempts: prev.attempts + 1 }));

        try {
          const response = await fetch('/api/payment/status');
          const result = await response.json();

          if (result.success && result.data.isPaymentProcessed) {
            setPaymentStatus(prev => ({ ...prev, isProcessed: true, isChecking: false }));
            // Wait a moment to show success, then redirect
            setTimeout(() => {
              router.push(config.auth.callbackUrl);
            }, 2000);
          } else {
            setPaymentStatus(prev => ({ ...prev, isChecking: false }));
          }
        } catch (error) {
          console.error('Payment status check failed:', error);
          setPaymentStatus(prev => ({ ...prev, isChecking: false }));
        }
      };

      // Start checking immediately
      checkPaymentStatus();

      // Then check every 10 seconds
      const interval = setInterval(checkPaymentStatus, 1000);

      return () => clearInterval(interval);
    }
  }, [session, paymentStatus.isProcessed, paymentStatus.isChecking, paymentStatus.attempts, paymentStatus.maxAttempts, router]);

  const handleEmailSignIn = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn("email", {
        email,
        redirect: false,
        callbackUrl: config.auth.callbackUrl,
      });

      if (result?.ok) {
        setEmailSent(true);
      }
    } catch (error) {
      console.error("Sign in error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProviderSignIn = (providerId) => {
    signIn(providerId, { callbackUrl: config.auth.callbackUrl });
  };

  // Loading state while checking session
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-100">
        <LoadingSpinner variant="gradient" size="lg" text="Loading..." />
      </div>
    );
  }

  // Authenticated user - show success and redirect
  if (session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-100">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <Link href="/" className="inline-block">
              <span className="font-extrabold text-3xl">
                Stalk<span className="text-primary">Api</span>
              </span>
            </Link>

            {/* Success Icon */}
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mt-6">
              <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>

            <h2 className="mt-6 text-3xl font-extrabold text-base-content">
              Payment Successful!
            </h2>
            <p className="mt-2 text-sm text-base-content/70">
              Thank you for your payment, <strong>{session.user?.name || session.user?.email}</strong>
            </p>

            {paymentStatus.isProcessed ? (
              <p className="mt-4 text-xs text-green-600">
                ✅ Payment processed! Redirecting to your dashboard...
              </p>
            ) : (
              <div className="mt-4 space-y-2">
                <p className="text-xs text-base-content/70">
                  🔄 Processing your payment and setting up your account...
                </p>
                <p className="text-xs text-base-content/50">
                  This usually takes 10-30 seconds. Please wait.
                </p>
                {paymentStatus.attempts > 15 && (
                  <p className="text-xs text-warning">
                    Taking longer than expected. Your payment is being processed.
                  </p>
                )}
              </div>
            )}
          </div>

          <div className="text-center">
            <LoadingSpinner
              variant="gradient"
              size="lg"
              text={paymentStatus.isProcessed
                ? "Redirecting to dashboard..."
                : `Processing payment... (${paymentStatus.attempts}/${paymentStatus.maxAttempts})`
              }
            />
          </div>

          {/* <div className="text-center">
            {paymentStatus.attempts >= paymentStatus.maxAttempts && !paymentStatus.isProcessed ? (
              <div className="space-y-2">
                <p className="text-xs text-warning mb-2">
                  Payment processing is taking longer than expected.
                </p>
                <Link
                  href={config.auth.callbackUrl}
                  className="btn btn-primary btn-sm"
                >
                  Go to Dashboard Now
                </Link>
                <p className="text-xs text-base-content/50">
                  Your account will be activated shortly.
                </p>
              </div>
            ) : (
              <Link
                href={config.auth.callbackUrl}
                className="text-primary hover:text-primary-focus text-sm"
              >
                Go to Dashboard Now →
              </Link>
            )}
          </div> */}
        </div>
      </div>
    );
  }

  // Email sent confirmation for unauthenticated users
  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-100">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <Link href="/" className="inline-block">
              <span className="font-extrabold text-3xl">
                Stalk<span className="text-primary">Api</span>
              </span>
            </Link>

            {/* Success Icon */}
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mt-6">
              <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>

            <h2 className="mt-6 text-3xl font-extrabold text-base-content">
              Payment Successful!
            </h2>
            <p className="mt-2 text-sm text-base-content/70">
              Thank you for your payment. We&apos;ve sent a magic link to <strong>{email}</strong>
            </p>
            <p className="mt-4 text-xs text-base-content/50">
              Click the link in the email to access your dashboard. The link will expire in 24 hours.
            </p>
          </div>
          <div className="text-center">
            <button
              onClick={() => setEmailSent(false)}
              className="text-primary hover:text-primary-focus text-sm"
            >
              ← Back to sign in
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Unauthenticated user - show payment success + signin form
  return (
    <div className="min-h-screen flex items-center justify-center bg-base-100">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <Link href="/" className="inline-block">
            <span className="font-extrabold text-3xl">
              Stalk<span className="text-primary">Api</span>
            </span>
          </Link>

          {/* Success Icon */}
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mt-6">
            <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>

          <h2 className="mt-6 text-3xl font-extrabold text-base-content">
            Payment Successful!
          </h2>
          <p className="mt-2 text-sm text-base-content/70">
            Thank you for your payment. Please sign in to access your dashboard.
          </p>
          <p className="mt-4 text-xs text-base-content/50">
            <strong>Important:</strong> Use the same email address you used for the payment.
          </p>
        </div>

        <div className="space-y-6">
          {/* Google Sign In */}
          {providers?.google && config.enabledAuth.google && (
            <button
              onClick={() => handleProviderSignIn("google")}
              className="w-full flex justify-center items-center px-4 py-3 border border-base-300 rounded-lg shadow-sm bg-base-200 text-base-content hover:bg-base-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Continue with Google
            </button>
          )}

          {/* Email Sign In */}
          {providers?.email && config.enabledAuth.email && (
            <>
              {providers?.google && config.enabledAuth.google && (
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-base-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-base-100 text-base-content/50">or</span>
                  </div>
                </div>
              )}

              <form onSubmit={handleEmailSignIn} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-base-content mb-2">
                    Email address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input input-bordered w-full"
                    placeholder="Enter your payment email"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isLoading || !email}
                  className="btn btn-primary w-full"
                >
                  {isLoading ? (
                    <>
                      <LoadingSpinner variant="spin" size="sm" color="white" />
                      Sending magic link...
                    </>
                  ) : (
                    "Access Dashboard"
                  )}
                </button>
              </form>
            </>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-base-content/50">
            Having trouble? Contact{" "}
            <a href={`mailto:${config.resend.supportEmail}`} className="text-primary hover:text-primary-focus">
              support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
