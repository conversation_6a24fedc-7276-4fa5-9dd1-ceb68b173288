import { NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";
import configFile from "@/config";
import { query } from "@/libs/postgres";
import { query as queryApi } from "@/libs/postgres_api";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

const randomApiKey = () => {
  // create a function that generates a 50 character long key containing chars 0-9 a-z A-Z randomly
  const chars =
    "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const keyLength = 50;
  let apiKey = "";
  for (let i = 0; i < keyLength; i++) {
    apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return apiKey;
};

const createApiAccess = async (email, tier) => {
  const apiKey = randomApiKey();
  const plan = configFile.stripe.plans.find((p) => p.tier  === tier);
  const creditAmount = plan.credits;
  const result = await queryApi(
    `
    INSERT INTO users 
      (id, email, password_hash, api_key, tier_id, credits_remaining, credits_used_this_month, total_credits_purchased, is_active, created_at, updated_at) 
    VALUES 
      (gen_random_uuid(), $1, '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', $2, $3, $4, 0, 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id
    `,
    [email.toLowerCase(), apiKey, tier, creditAmount]
  );
  return result.rows[0].id;
};

const updateApiAccess = async (id, tier) => {
  const plan = configFile.stripe.plans.find((p) => p.tier  === tier);
  const creditAmount = plan.credits;
  await queryApi(
    `UPDATE users SET tier_id = $1, credits_remaining = $2, credits_used_this_month = 0 WHERE id = $3`,
    [tier, creditAmount, id]
  );
}

const revokeApiAccess = async (id) => {
  // Set tier to 1 (free tier) and credits to 0 when subscription is cancelled
  await queryApi(
    `UPDATE users SET tier_id = 1, credits_remaining = 0, credits_used_this_month = 0, is_active = false WHERE id = $1`,
    [id]
  );
}

// This is where we receive Stripe webhook events
// It used to update the user data, send emails, etc...
// By default, it'll store the user in the database
// See more: https://shipfa.st/docs/features/payments
export async function POST(req) {
  const body = await req.text();

  const signature = headers().get("stripe-signature");

  let data;
  let eventType;
  let event;

  // verify Stripe event is legit
  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed. ${err.message}`);
    return NextResponse.json({ error: err.message }, { status: 400 });
  }

  data = event.data;
  eventType = event.type;

  await query(
    `INSERT INTO stripe_webhooks${
      process.env.NODE_ENV === "development" ? "_dev" : ""
    }
      (created_at, event_id, event_type, stripe_customer_id, event_data)
    VALUES
      (CURRENT_TIMESTAMP, $1, $2, $3, $4)`,
    [event.id, event.type, event?.data?.object?.customer, JSON.parse(body)]
  );

  try {
    switch (eventType) {
      case "checkout.session.completed": {
        // Checkout session completed - this event fires when the checkout is successful
        // However, we handle all payment processing in invoice.paid to avoid duplicates
        // and ensure consistent handling of both initial payments and renewals

        console.log(`Checkout session completed: ${data.object.id}`);

        // We can optionally log this event or perform non-critical actions here
        // All critical database updates are handled in invoice.paid event

        break;
      }

      case "checkout.session.expired": {
        // User didn't complete the transaction
        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance
        break;
      }

      case "customer.subscription.updated": {
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the "customer.subscription.deleted" event
        // You can update the user data to show a "Cancel soon" badge for instance
        break;
      }

      case "customer.subscription.deleted": {
        // The customer subscription stopped
        // ❌ Revoke access to the product and API access
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        const subscription = await stripe.subscriptions.retrieve(
          data.object.id
        );

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(
          subscription.customer
        );
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId: subscription.customer },
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        if (user) {
          // Revoke access to your product in main database
          const updatedUser = await prisma.user.update({
            where: { id: user.id },
            data: { hasAccess: false },
          });

          // Revoke API access in API database (set tier to 1, credits to 0)
          if (updatedUser.apiKey) {
            await revokeApiAccess(updatedUser.apiKey);
            console.log(`Revoked API access for user: ${user.id} (subscription cancelled)`);
          }

          // Update cache
          await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
          await cache.set(CacheKeys.userAccess(user.id), false, 3600);
          await cache.del(customerCacheKey); // Remove from cache to force refresh

          console.log(`Successfully revoked access for user: ${user.id} due to subscription cancellation`);
        } else {
          console.error(`No user found for subscription cancellation: ${subscription.customer}`);
        }

        break;
      }

      case "invoice.paid": {
        // Customer just paid an invoice - handles both initial payments and recurring renewals
        // This is the definitive "payment successful" event for all scenarios
        // ✅ Grant access to the product and manage API keys

        // Safely extract price ID with error handling - handle both real and test events
        let priceId = data.object.lines?.data?.[0]?.price?.id; // Real Stripe events
        if (!priceId) {
          priceId = data.object.lines?.data?.[0]?.pricing?.price_details?.price; // Stripe CLI test events
        }

        const customerId = data.object.customer;

        if (!priceId) {
          console.error(`No price ID found in invoice.paid event. Event data:`, JSON.stringify(data.object, null, 2));
          break;
        }

        let plan = configFile.stripe.plans.find((p) => p.priceId === priceId);

        // For testing with Stripe CLI, use a default plan if no match found
        if (!plan) {
          console.warn(`No plan found for priceId: ${priceId}. Using default test plan for webhook testing.`);

          // Create a default test plan for Stripe CLI testing
          plan = {
            priceId: priceId,
            name: "Test Plan",
            tier: 2,
            credits: 1000000,
            description: "Test plan for webhook testing",
            price: 20.00
          };

          console.log(`Using test plan: ${plan.name} for priceId: ${priceId}`);
        }

        // Get customer details from Stripe
        const customer = await stripe.customers.retrieve(customerId);

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(customerId);
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId },
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        // If no user found by customerId, try to find by email or create new user
        if (!user) {
          // Handle test customers without email by creating a test email
          let email = customer.email;
          if (!email) {
            email = `test-${customerId}@stripe-cli-test.com`;
            console.log(`Created test email for Stripe CLI customer: ${email}`);
          }
          email = email.toLowerCase().trim();

          // Check cache first
          const emailCacheKey = CacheKeys.userByEmail(email);
          user = await cache.get(emailCacheKey);

          if (!user) {
            user = await prisma.user.findUnique({
              where: { email },
            });

            if (!user) {
              // Create new user for this payment
              user = await prisma.user.create({
                data: {
                  email,
                  name: customer.name,
                },
              });
              console.log(`Created new user for invoice payment: ${user.id}`);
            }

            // Cache user for 1 hour
            await cache.set(emailCacheKey, user, 3600);
            await cache.set(CacheKeys.user(user.id), user, 3600);
          }
        }

        if (!user) {
          console.error(`No user found for customerId: ${customerId}`);
          break;
        }

        // Update user data + Grant user access to your product
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: {
            priceId,
            customerId,
            hasAccess: true,
          },
        });

        // Handle API key creation/update for the API database
        if (!updatedUser.apiKey) {
          // Create new API access for first-time customers
          const apiKeyId = await createApiAccess(updatedUser.email, plan.tier);
          await prisma.user.update({
            where: { id: user.id },
            data: {
              apiKey: apiKeyId,
            },
          });
          console.log(`Created API access for new user: ${user.id}`);
        } else {
          // Update existing API access for renewals
          await updateApiAccess(updatedUser.apiKey, plan.tier);
          console.log(`Updated API access for existing user: ${user.id}`);
        }

        // Update cache with new user data
        await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
        if (updatedUser.email) {
          await cache.set(
            CacheKeys.userByEmail(updatedUser.email),
            updatedUser,
            3600
          );
        }
        await cache.set(CacheKeys.userAccess(user.id), true, 3600);
        await cache.set(customerCacheKey, updatedUser, 3600);

        console.log(`Successfully processed invoice payment for user: ${user.id}, plan: ${plan.name}`);

        break;
      }

      case "invoice.payment_failed":
        // A payment failed (for instance the customer does not have a valid payment method)
        // ❌ Revoke access to the product
        // ⏳ OR wait for the customer to pay (more friendly):
        //      - Stripe will automatically email the customer (Smart Retries)
        //      - We will receive a "customer.subscription.deleted" when all retries were made and the subscription has expired

        break;

      default:
      // Unhandled event type
    }
  } catch (e) {
    console.error("stripe error: " + e.message + " | EVENT TYPE: " + eventType);
  }

  return NextResponse.json({});
}
