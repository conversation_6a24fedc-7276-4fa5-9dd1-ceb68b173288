import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { query as queryApi } from "@/libs/postgres_api";
import configFile from "@/config";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";

const randomApiKey = () => {
  // create a function that generates a 50 character long key containing chars 0-9 a-z A-Z randomly
  const chars =
    "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const keyLength = 50;
  let apiKey = "";
  for (let i = 0; i < keyLength; i++) {
    apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return apiKey;
};

const updateApiAccess = async (id, tier) => {
  const plan = configFile.stripe.plans.find((p) => p.tier === tier);
  const creditAmount = plan.credits;
  await queryApi(
    `UPDATE users SET tier_id = $1, credits_remaining = $2, credits_used_this_month = 0 WHERE id = $3`,
    [tier, creditAmount, id]
  );
};

export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Regenerate API key logic here
    const newKey = await updateApiAccess(user.apiKey, user.tier_id);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        apiKey: apiKeyId,
      },
    });

    // For now, we'll just return a success response
    return NextResponse.json({ success: true, session });
  } catch (error) {
    console.error("API key regeneration error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
