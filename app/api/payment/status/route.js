import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";

// API endpoint to check if payment has been processed by Stripe webhook
// This is used by the payment success page to know when to redirect to dashboard
export async function GET() {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get user data from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        hasAccess: true,
        priceId: true,
        customerId: true,
        apiKey: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Determine if payment has been processed
    // Payment is considered processed if user has:
    // 1. hasAccess = true (granted by webhook)
    // 2. priceId (set by webhook when payment is processed)
    // 3. customerId (set by webhook)
    // 4. apiKey (created/updated by webhook)
    const isPaymentProcessed = Boolean(
      user.hasAccess && 
      user.priceId && 
      user.customerId && 
      user.apiKey
    );

    // Calculate how long since user was created/updated
    const now = new Date();
    const timeSinceCreated = now - new Date(user.createdAt);
    const timeSinceUpdated = now - new Date(user.updatedAt);
    
    // If user was recently updated (within last 5 minutes), it's likely from webhook
    const recentlyUpdated = timeSinceUpdated < 5 * 60 * 1000; // 5 minutes

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        email: user.email,
        isPaymentProcessed,
        hasAccess: user.hasAccess,
        hasPriceId: Boolean(user.priceId),
        hasCustomerId: Boolean(user.customerId),
        hasApiKey: Boolean(user.apiKey),
        recentlyUpdated,
        timeSinceCreated: Math.floor(timeSinceCreated / 1000), // seconds
        timeSinceUpdated: Math.floor(timeSinceUpdated / 1000), // seconds
        // Don't expose sensitive data like actual API key
        processingStatus: isPaymentProcessed ? "completed" : "pending",
      },
    });

  } catch (error) {
    console.error("Payment status check error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
