"use client";

import { useState, useEffect } from "react";
import { getProviders, signIn, getSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import config from "@/config";

export default function SignIn() {
  const [providers, setProviders] = useState(null);
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || config.auth.callbackUrl;
  const error = searchParams.get("error");

  useEffect(() => {
    const setUpProviders = async () => {
      const response = await getProviders();
      setProviders(response);
    };
    setUpProviders();
  }, []);

  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession();
      if (session) {
        router.push(callbackUrl);
      }
    };
    checkSession();
  }, [callbackUrl, router]);

  const handleEmailSignIn = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const result = await signIn("email", {
        email,
        redirect: false,
        callbackUrl,
      });
      
      if (result?.ok) {
        setEmailSent(true);
      }
    } catch (error) {
      console.error("Sign in error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProviderSignIn = (providerId) => {
    signIn(providerId, { callbackUrl });
  };

  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-100">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-base-content">
              Check your email
            </h2>
            <p className="mt-2 text-sm text-base-content/70">
              We've sent a magic link to <strong>{email}</strong>
            </p>
            <p className="mt-4 text-xs text-base-content/50">
              Click the link in the email to sign in. The link will expire in 24 hours.
            </p>
          </div>
          <div className="text-center">
            <button
              onClick={() => setEmailSent(false)}
              className="text-primary hover:text-primary-focus text-sm"
            >
              ← Back to sign in
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-base-100">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <Link href="/" className="inline-block">
            <span className="font-extrabold text-3xl">
              Stalk<span className="text-primary">Api</span>
            </span>
          </Link>
          <h2 className="mt-6 text-3xl font-extrabold text-base-content">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-base-content/70">
            Access your API dashboard and manage your services
          </p>
        </div>

        {error && (
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>
              {error === "OAuthSignin" && "Error occurred during sign in"}
              {error === "OAuthCallback" && "Error occurred during sign in"}
              {error === "OAuthCreateAccount" && "Could not create account"}
              {error === "EmailCreateAccount" && "Could not create account"}
              {error === "Callback" && "Error occurred during sign in"}
              {error === "OAuthAccountNotLinked" && "Account already exists with different provider"}
              {error === "EmailSignin" && "Check your email for the sign in link"}
              {error === "CredentialsSignin" && "Invalid credentials"}
              {error === "SessionRequired" && "Please sign in to access this page"}
              {!["OAuthSignin", "OAuthCallback", "OAuthCreateAccount", "EmailCreateAccount", "Callback", "OAuthAccountNotLinked", "EmailSignin", "CredentialsSignin", "SessionRequired"].includes(error) && "An error occurred"}
            </span>
          </div>
        )}

        <div className="space-y-6">
          {/* Google Sign In */}
          {providers?.google && config.enabledAuth.google && (
            <button
              onClick={() => handleProviderSignIn("google")}
              className="w-full flex justify-center items-center px-4 py-3 border border-base-300 rounded-lg shadow-sm bg-base-200 text-base-content hover:bg-base-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Continue with Google
            </button>
          )}

          {/* Email Sign In */}
          {providers?.email && config.enabledAuth.email && (
            <>
              {providers?.google && config.enabledAuth.google && (
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-base-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-base-100 text-base-content/50">or</span>
                  </div>
                </div>
              )}

              <form onSubmit={handleEmailSignIn} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-base-content mb-2">
                    Email address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input input-bordered w-full"
                    placeholder="Enter your email"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isLoading || !email}
                  className="btn btn-primary w-full"
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Sending magic link...
                    </>
                  ) : (
                    "Sign in with Email"
                  )}
                </button>
              </form>
            </>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-base-content/50">
            By signing in, you agree to our{" "}
            <Link href="/privacy-policy" className="text-primary hover:text-primary-focus">
              Privacy Policy
            </Link>{" "}
            and{" "}
            <Link href="/tos" className="text-primary hover:text-primary-focus">
              Terms of Service
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
