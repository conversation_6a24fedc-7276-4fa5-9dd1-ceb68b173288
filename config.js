import themes from "daisyui/src/theming/themes";

const config = {
  // REQUIRED
  appName: "StalkAPI",
  // REQUIRED: a short description of your app for SEO tags (can be overwritten)
  appDescription:
    "Professional API services platform with credit-based usage tracking, real-time streaming, and enterprise-grade infrastructure.",
  // REQUIRED (no https://, not trialing slash at the end, just the naked domain)
  domainName: process.env.NODE_ENV === "development" ? "localhost:3000" : "stalkapi.com",
  domainUrl: process.env.NODE_ENV === "development" ? "http://localhost:3000" : "https://stalkapi.com",
  crisp: {
    // Crisp website ID. IF YOU DON'T USE CRISP: just remove this => Then add a support email in this config file (resend.supportEmail) otherwise customer support won't work.
    id: "",
    // Hide Crisp by default, except on route "/". Crisp is toggled with <ButtonSupport/>. If you want to show Crisp on every routes, just remove this below
    onlyShowOnRoutes: ["/"],
  },
  stripe: {
    // Create multiple plans in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId
    plans: [
      {
        // REQUIRED — we use this to find the plan in the webhook (for instance if you want to update the user's credits based on the plan)
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1RYHd0CxyvcNUSW35tQ6qPik"
            : "price_456",
        //  REQUIRED - Name of the plan, displayed on the pricing page
        name: "Basic",
        tier: 2,
        credits: 1000000,
        // A friendly description of the plan, displayed on the pricing page. Tip: explain why this plan and not others
        description: "Get started with the basics.",
        // The price you want to display, the one user will be charged on Stripe.
        price: 499.99,
        // If you have an anchor price (i.e. $29) that you want to display crossed out, put it here. Otherwise, leave it empty
        // priceAnchor: 99.99,
        features: [
          {
            name: "1,000,000 API credits/month",
          },
          { name: "60 requests/minute" },
          { name: "3 WebSocket connections" },
          // { name: "Basic email support" },
          // { name: "API documentation" },
          { name: "KOL Feed" },
          { name: "Smart Money Endpoints" },
          { name: "Token Price & Metadata" },
        ],
      },
      {
        // This plan will look different on the pricing page, it will be highlighted. You can only have one plan with isFeatured: true
        isFeatured: true,
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1RYHeUCxyvcNUSW3kv2TKS2w"
            : "price_456",
        name: "Premium",
        tier: 3,
        credits: 5000000,
        description: "For additional websocket streams.",
        price: 1499.99,
        // priceAnchor: 299.99,
        features: [
          {
            name: "5,000,000 API credits/month",
          },
          { name: "300 requests/minute" },
          { name: "5 WebSocket connections" },
          { name: "All Basic endpoints" },
          { name: "Stream: wallet transactions" },
          { name: "Stream: new token launches" },
          { name: "Stream: graduating & graduated tokens" },
          { name: "Stream: token transactions & changes" },
          { name: "Stream: token price changes" },
          { name: "Stream: token holders" },
          // { name: "Priority support" },
          // { name: "Advanced features" },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1RYHf9CxyvcNUSW3NIQnfxbm"
            : "price_456",
        name: "Enterprise",
        tier: 4,
        credits: -1,
        description: "For large-scale applications and enterprises",
        price: 4999.99,
        // priceAnchor: 999.99,
        features: [
          {
            name: "Unlimited API credits",
          },
          { name: "1,000 requests/minute" },
          { name: "10 WebSocket connections" },
          { name: "All Basic & Premium endpoints" },
          { name: "Stream: Realtime Jupiter AMM swap events" },
          { name: "Stream: Realtime Jupiter DCA events" },
          { name: "Stream: Realtime Pump.fun AMM swap events" },
          { name: "Personal onboarding call" },
          // { name: "Batch processing" },
          // { name: "24/7 dedicated support" },
          // { name: "Custom integrations" },
          // { name: "SLA guarantee" },
        ],
      },
    ],
  },
  aws: {
    // If you use AWS S3/Cloudfront, put values in here
    bucket: "bucket-name",
    bucketUrl: `https://bucket-name.s3.amazonaws.com/`,
    cdn: "https://cdn-id.cloudfront.net/",
  },
  resend: {
    // REQUIRED — Email 'From' field to be used when sending magic login links
    fromNoReply: `StalkAPI <<EMAIL>>`,
    // REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..
    fromAdmin: `StalkAPI Team <<EMAIL>>`,
    // Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support."
    supportEmail: "<EMAIL>",
  },
  colors: {
    // REQUIRED — The DaisyUI theme to use (added to the main layout.js). Leave blank for default (light & dark mode). If you any other theme than light/dark, you need to add it in config.tailwind.js in daisyui.themes.
    theme: "", // Leave blank to enable both light and dark themes
    // REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..). By default it takes the primary color from your DaisyUI theme (make sure to update your the theme name after "data-theme=")
    // OR you can just do this to use a custom color: main: "#f37055". HEX only.
    main: themes["dark"]["primary"],
  },
  auth: {
    // REQUIRED — the path to log in users. It's use to protect private routes (like /dashboard). It's used in apiClient (/libs/api.js) upon 401 errors from our API
    loginUrl: "/auth/signin",
    // REQUIRED — the path you want to redirect users after successfull login (i.e. /dashboard, /private). This is normally a private page for users to manage their accounts. It's used in apiClient (/libs/api.js) upon 401 errors from our API & in ButtonSignin.js
    callbackUrl: "/dashboard",
  },
  // Authentication providers configuration - easily enable/disable auth methods
  enabledAuth: {
    google: false,        // Enable/disable Google OAuth login
    email: true,        // Enable/disable email magic link login
    // Add more providers here as needed:
    // github: false,
    // discord: false,
    // apple: false,
  },
};

// Helper functions for auth configuration
export const getEnabledAuthProviders = () => {
  return Object.entries(config.enabledAuth)
    .filter(([, enabled]) => enabled)
    .map(([provider]) => provider);
};

export const isAnyAuthEnabled = () => {
  return Object.values(config.enabledAuth).some(enabled => enabled);
};

export const isAuthProviderEnabled = (provider) => {
  return config.enabledAuth[provider] === true;
};

export default config;
