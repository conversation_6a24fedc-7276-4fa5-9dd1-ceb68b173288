{"name": "ship-fast-code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "node scripts/postbuild.js", "start": "next start", "lint": "next lint", "db:setup": "node scripts/setup-database.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "security:test": "npm test test/security-audit.test.js", "security:enable-ssl": "node scripts/enable-ssl-validation.js", "test:env": "node scripts/test-env.js", "stripe-webhook": "stripe listen --forward-to localhost:3000/api/webhook/stripe"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@auth/prisma-adapter": "^1.4.0", "@headlessui/react": "^1.7.18", "@prisma/client": "^5.9.1", "axios": "^1.6.8", "crisp-sdk-web": "^1.0.22", "dotenv": "^16.5.0", "eslint": "8.47.0", "eslint-config-next": "13.4.19", "form-data": "^4.0.0", "ioredis": "^5.3.2", "next": "^14.1.4", "next-auth": "^4.24.7", "next-sitemap": "^4.2.3", "nextjs-toploader": "^1.6.11", "nodemailer": "^6.9.13", "pg": "^8.16.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.26.3", "resend": "^4.0.1", "sharp": "^0.34.2", "stripe": "^13.11.0", "zod": "^3.22.2"}, "devDependencies": {"autoprefixer": "^10.4.19", "daisyui": "^4.10.1", "postcss": "^8.4.38", "prisma": "^5.9.1", "tailwindcss": "^3.4.3"}}