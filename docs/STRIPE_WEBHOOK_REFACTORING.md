# Stripe Webhook Handler Refactoring

## Overview

This document describes the refactoring of the Stripe webhook handler to improve payment processing reliability and consistency. The main change involves moving database update logic from `checkout.session.completed` to `invoice.paid` event handler.

## Changes Made

### Before Refactoring

**`checkout.session.completed` handler:**
- Handled extensive database operations
- Created/updated users
- Managed API key creation/updates
- Updated cache
- Processed both initial payments and renewals inconsistently

**`invoice.paid` handler:**
- Only granted access (`hasAccess: true`)
- Limited functionality
- Did not handle API key management

### After Refactoring

**`checkout.session.completed` handler:**
- Simplified to minimal logging
- No database operations
- Prevents duplicate processing

**`invoice.paid` handler:**
- Comprehensive payment processing
- Handles both new customers and renewals
- Manages API key creation/updates
- Full cache management
- Centralized payment success logic

**`customer.subscription.deleted` handler:**
- Revokes access in main database
- Sets API tier to 1 (free tier)
- Sets credits to 0 in API database
- Deactivates API access
- Updates cache appropriately

## Benefits of the Refactoring

### 1. Unified Payment Handling
- **Single Source of Truth**: All payment processing happens in `invoice.paid`
- **Consistent Behavior**: Both initial payments and renewals follow the same code path
- **Reduced Complexity**: Eliminates duplicate logic between event handlers

### 2. Prevents Duplicate Processing
- **Race Condition Prevention**: Avoids conflicts when both events fire simultaneously
- **Idempotent Operations**: Safe to process the same invoice multiple times
- **Data Integrity**: Ensures consistent database state

### 3. More Reliable Payment Processing
- **Definitive Payment Event**: `invoice.paid` is the authoritative "payment successful" signal
- **Handles All Scenarios**: Works for subscriptions, one-time payments, and renewals
- **Better Error Handling**: Centralized error management for payment processing

### 4. Improved Subscription Management
- **Renewal Consistency**: Subscription renewals only trigger `invoice.paid`, not `checkout.session.completed`
- **Plan Changes**: Handles plan upgrades/downgrades through the same logic
- **Failed Payment Recovery**: Consistent handling when payments are retried

## Technical Implementation Details

### Database Operations in `invoice.paid`

1. **User Resolution**:
   ```javascript
   // Try to find user by customerId first (cached)
   // Fall back to email lookup if needed
   // Create new user if none exists
   ```

2. **API Key Management**:
   ```javascript
   if (!updatedUser.apiKey) {
     // Create new API access for first-time customers
     const apiKeyId = await createApiAccess(updatedUser.email, plan.tier);
   } else {
     // Update existing API access for renewals
     await updateApiAccess(updatedUser.apiKey, plan.tier);
   }
   ```

3. **Cache Management**:
   ```javascript
   // Update multiple cache keys for optimal performance
   await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
   await cache.set(CacheKeys.userByEmail(updatedUser.email), updatedUser, 3600);
   await cache.set(CacheKeys.userAccess(user.id), true, 3600);
   await cache.set(customerCacheKey, updatedUser, 3600);
   ```

### Database Operations in `customer.subscription.deleted`

1. **Access Revocation**:
   ```javascript
   // Revoke access in main database
   const updatedUser = await prisma.user.update({
     where: { id: user.id },
     data: { hasAccess: false },
   });
   ```

2. **API Access Revocation**:
   ```javascript
   // Set tier to 1, credits to 0, deactivate API access
   await revokeApiAccess(updatedUser.apiKey);
   ```

3. **Cache Updates**:
   ```javascript
   // Update cache to reflect revoked access
   await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
   await cache.set(CacheKeys.userAccess(user.id), false, 3600);
   await cache.del(customerCacheKey); // Force refresh
   ```

### Error Handling Improvements

- **Plan Validation**: Ensures valid `priceId` before processing
- **User Validation**: Handles missing users gracefully
- **Logging**: Comprehensive logging for debugging and monitoring
- **Graceful Degradation**: Continues processing other webhooks if one fails

## Testing with Stripe CLI

### Prerequisites
1. Install Stripe CLI: `brew install stripe/stripe-cli/stripe` (macOS) or [download from Stripe](https://stripe.com/docs/stripe-cli)
2. Login to Stripe: `stripe login`
3. Start your Next.js development server: `pnpm dev`

### Testing the Refactored Webhook

#### 1. Start Webhook Forwarding
```bash
# Forward Stripe webhook events to your local server
pnpm stripe-webhook
# or directly:
stripe listen --forward-to localhost:3000/api/webhook/stripe
```

#### 2. Test Invoice Paid Event (Primary Test)
```bash
# Test new customer payment
stripe trigger invoice.payment_succeeded

# Test with specific customer
stripe trigger invoice.payment_succeeded --add customer:email=<EMAIL>
```

#### 3. Test Checkout Session Completed (Should be Minimal)
```bash
# This should now only log the event without database operations
stripe trigger checkout.session.completed
```

#### 4. Test Error Scenarios
```bash
# Test with invalid price ID (should be handled gracefully)
stripe trigger invoice.payment_succeeded --override lines.data.0.price.id=price_invalid
```

### What to Verify

#### Invoice Paid Event Should:
1. **Create new users** when customer doesn't exist
2. **Update existing users** for renewals
3. **Create API keys** for new customers
4. **Update API access** for existing customers
5. **Update cache** comprehensively
6. **Log success messages** with user ID and plan name

#### Checkout Session Completed Should:
1. **Only log** the event ID
2. **Not perform** database operations
3. **Complete quickly** without errors

### Monitoring Logs
Watch your Next.js console for these log messages:

**Success Logs:**
```
Created new user for invoice payment: user_xxx
Created API access for new user: user_xxx
Updated API access for existing user: user_xxx
Successfully processed invoice payment for user: user_xxx, plan: Basic
```

**Error Logs:**
```
No plan found for priceId: price_xxx
No user found for customerId: cus_xxx
```

## Monitoring and Debugging

### Log Messages
The refactored webhook includes detailed logging:

```javascript
console.log(`Created new user for invoice payment: ${user.id}`);
console.log(`Created API access for new user: ${user.id}`);
console.log(`Updated API access for existing user: ${user.id}`);
console.log(`Successfully processed invoice payment for user: ${user.id}, plan: ${plan.name}`);
```

### Error Logging
```javascript
console.error(`No plan found for priceId: ${priceId}`);
console.error(`No user found for customerId: ${customerId}`);
```

## Migration Considerations

### Backward Compatibility
- Existing webhooks continue to work
- No breaking changes to external APIs
- Database schema remains unchanged

### Deployment
- Zero-downtime deployment
- No manual migration steps required
- Webhook endpoints remain the same

## Best Practices Implemented

1. **Event-Driven Architecture**: Proper separation of concerns between webhook events
2. **Idempotency**: Safe to replay webhook events
3. **Caching Strategy**: Efficient cache usage to reduce database load
4. **Error Resilience**: Graceful handling of edge cases
5. **Comprehensive Logging**: Detailed logs for monitoring and debugging

## Future Enhancements

### Potential Improvements
1. **Webhook Retry Logic**: Implement exponential backoff for failed webhooks
2. **Event Deduplication**: Add webhook event ID tracking to prevent duplicates
3. **Metrics Collection**: Add performance and success rate metrics
4. **Email Notifications**: Send confirmation emails on successful payments

### Configuration Options
Consider adding configuration flags for:
- Enabling/disabling specific webhook events
- Customizing retry behavior
- Adjusting cache TTL values

## Conclusion

This refactoring significantly improves the reliability and maintainability of the Stripe webhook handler. By centralizing payment processing in the `invoice.paid` event, we ensure consistent behavior across all payment scenarios while reducing the risk of duplicate processing and data inconsistencies.

The changes maintain backward compatibility while providing a more robust foundation for future payment-related features.
