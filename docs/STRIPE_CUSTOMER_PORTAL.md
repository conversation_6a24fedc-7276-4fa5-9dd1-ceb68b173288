# Stripe Customer Portal Integration

This document describes the implementation of Stripe Customer Portal functionality in the StatsOverview component, allowing users to manage their subscriptions directly through Stripe's hosted portal.

## Overview

The Stripe Customer Portal integration provides users with a secure way to manage their subscription, payment methods, billing history, and download invoices through Stripe's hosted portal interface.

## Implementation Details

### Component Integration

The functionality is integrated into the `StatsOverview` component (`components/dashboard/StatsOverview.js`) within the "Your Current Plan" card.

### Key Features

- **Secure Server-Side Processing**: All Stripe operations are handled server-side
- **Loading State Management**: Visual feedback during portal creation
- **Error Handling**: Comprehensive error handling with user notifications
- **Authentication Required**: Only authenticated users with Stripe customer IDs can access
- **Automatic Return**: Users are returned to the dashboard after portal session

### Code Structure

```javascript
const StatsOverview = ({ data }) => {
  const [isLoadingPortal, setIsLoadingPortal] = useState(false);

  const openStripePortal = async () => {
    setIsLoadingPortal(true);
    
    try {
      const response = await apiClient.post("/stripe/create-portal", {
        returnUrl: window.location.origin + "/dashboard"
      });
      
      window.location.href = response.url;
    } catch (error) {
      console.error("Failed to open Stripe portal:", error);
    } finally {
      setIsLoadingPortal(false);
    }
  };
  
  // Button implementation with loading state
  <button
    className="mt-1 text-sm font-medium text-purple-600 dark:text-purple-400 hover:underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
    onClick={openStripePortal}
    disabled={isLoadingPortal}
  >
    {isLoadingPortal ? (
      <>
        <span className="loading loading-spinner loading-xs"></span>
        Opening...
      </>
    ) : (
      "Manage Plan"
    )}
  </button>
};
```

## API Integration

### Endpoint Used

- **Route**: `/api/stripe/create-portal`
- **Method**: POST
- **Authentication**: Required (NextAuth session)

### Request Payload

```json
{
  "returnUrl": "https://yourdomain.com/dashboard"
}
```

### Response

```json
{
  "url": "https://billing.stripe.com/session/bps_1234567890"
}
```

### Error Responses

```json
{
  "error": "Authentication required"
}
```

```json
{
  "error": "You don't have a billing account yet. Make a purchase first."
}
```

## Security Considerations

### Server-Side Only Operations

- Stripe secret key is never exposed to the frontend
- Customer portal creation happens entirely on the server
- User authentication is verified before portal creation

### Input Validation

- Return URL is validated using Zod schema
- User session is verified through NextAuth
- Customer ID existence is checked before portal creation

### Error Handling

- Sensitive error details are not exposed to frontend
- Generic error messages are shown to users
- Detailed errors are logged server-side for debugging

## User Experience

### Loading States

1. **Initial State**: "Manage Plan" button is clickable
2. **Loading State**: Button shows spinner and "Opening..." text, becomes disabled
3. **Success**: User is redirected to Stripe portal
4. **Error**: Button returns to initial state, error toast is shown

### Portal Features

Users can access the following features in the Stripe portal:

- **Subscription Management**: Cancel, pause, or modify subscriptions
- **Payment Methods**: Add, remove, or update payment methods
- **Billing History**: View and download past invoices
- **Subscription Details**: View current plan details and next billing date

## Testing

### Automated Tests

Run the test suite:

```bash
npm test test/stripe-portal-integration.test.js
```

### Manual Testing

1. **Prerequisites**:
   - User must be authenticated
   - User must have a Stripe customer ID in the database
   - Stripe environment variables must be configured

2. **Test Steps**:
   - Login to the application
   - Navigate to the dashboard
   - Locate the "Your Current Plan" card
   - Click "Manage Plan" button
   - Verify loading state appears
   - Confirm redirect to Stripe portal
   - Test portal functionality
   - Use return link to come back to dashboard

3. **Error Cases**:
   - Test with user without customer ID
   - Test with unauthenticated user
   - Test with invalid Stripe configuration

## Troubleshooting

### Common Issues

1. **"You don't have a billing account yet"**
   - User needs to make a purchase first to create a Stripe customer
   - Check if user has `customerId` in the database

2. **Authentication errors**
   - Verify user is logged in
   - Check NextAuth configuration
   - Ensure session is valid

3. **Portal creation fails**
   - Verify Stripe secret key is configured
   - Check Stripe account status
   - Review server logs for detailed errors

### Debug Steps

1. Check browser console for frontend errors
2. Review server logs for API errors
3. Verify database user record has `customerId`
4. Test Stripe API key with Stripe CLI
5. Confirm return URL is accessible

## Configuration

### Environment Variables

Required environment variables:

```env
STRIPE_SECRET_KEY=sk_test_...
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://yourdomain.com
```

### Stripe Dashboard Configuration

1. Enable Customer Portal in Stripe Dashboard
2. Configure portal settings (allowed features)
3. Set up webhook endpoints for subscription changes
4. Test with Stripe test mode before production

## Future Enhancements

### Potential Improvements

- **Portal Customization**: Custom branding and styling
- **Feature Restrictions**: Limit available portal features per plan
- **Analytics Integration**: Track portal usage and conversion
- **Mobile Optimization**: Enhanced mobile portal experience

### Integration Opportunities

- **Webhook Handling**: Process subscription changes from portal
- **Usage Alerts**: Notify users of plan changes
- **Support Integration**: Connect portal with customer support
- **Billing Notifications**: Email notifications for billing events

## Related Documentation

- [Stripe Integration Guide](./STRIPE_INTEGRATION.md)
- [Authentication System](./AUTHENTICATION.md)
- [API Routes Documentation](./API_ROUTES.md)
- [Security Guide](./SECURITY_GUIDE.md)
