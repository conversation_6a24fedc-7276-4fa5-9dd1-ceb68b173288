"use client";

import React from "react";

// Beautiful loading spinner component with multiple animation options
const LoadingSpinner = ({ 
  size = "md", 
  variant = "pulse", 
  color = "primary",
  className = "",
  text = null 
}) => {
  // Size configurations
  const sizes = {
    xs: "w-4 h-4",
    sm: "w-6 h-6", 
    md: "w-8 h-8",
    lg: "w-12 h-12",
    xl: "w-16 h-16"
  };

  // Color configurations
  const colors = {
    primary: "text-primary",
    secondary: "text-secondary", 
    accent: "text-accent",
    neutral: "text-neutral",
    info: "text-info",
    success: "text-success",
    warning: "text-warning",
    error: "text-error",
    white: "text-white",
    gray: "text-gray-500"
  };

  const sizeClass = sizes[size] || sizes.md;
  const colorClass = colors[color] || colors.primary;

  // Different animation variants
  const renderSpinner = () => {
    switch (variant) {
      case "dots":
        return (
          <div className={`flex space-x-1 ${sizeClass} ${colorClass} ${className}`}>
            <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
          </div>
        );

      case "pulse":
        return (
          <div className={`${sizeClass} ${colorClass} ${className}`}>
            <div className="relative">
              <div className="w-full h-full bg-current rounded-full animate-ping opacity-75"></div>
              <div className="absolute inset-0 w-full h-full bg-current rounded-full animate-pulse"></div>
            </div>
          </div>
        );

      case "spin":
        return (
          <div className={`${sizeClass} ${colorClass} ${className}`}>
            <svg className="animate-spin" fill="none" viewBox="0 0 24 24">
              <circle 
                className="opacity-25" 
                cx="12" 
                cy="12" 
                r="10" 
                stroke="currentColor" 
                strokeWidth="4"
              ></circle>
              <path 
                className="opacity-75" 
                fill="currentColor" 
                d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        );

      case "orbit":
        return (
          <div className={`${sizeClass} ${colorClass} ${className}`}>
            <div className="relative">
              <div className="w-full h-full border-2 border-current border-opacity-20 rounded-full"></div>
              <div className="absolute top-0 left-0 w-full h-full border-2 border-transparent border-t-current rounded-full animate-spin"></div>
              <div className="absolute top-1 left-1 w-2 h-2 bg-current rounded-full animate-ping"></div>
            </div>
          </div>
        );

      case "ripple":
        return (
          <div className={`${sizeClass} ${colorClass} ${className}`}>
            <div className="relative">
              <div className="absolute inset-0 border-2 border-current rounded-full animate-ping"></div>
              <div className="absolute inset-0 border-2 border-current rounded-full animate-ping [animation-delay:0.5s]"></div>
              <div className="absolute inset-2 bg-current rounded-full"></div>
            </div>
          </div>
        );

      case "bars":
        return (
          <div className={`flex items-end space-x-1 ${sizeClass} ${colorClass} ${className}`}>
            <div className="w-1 bg-current rounded-full animate-pulse [animation-delay:-0.4s]" style={{height: '60%'}}></div>
            <div className="w-1 bg-current rounded-full animate-pulse [animation-delay:-0.2s]" style={{height: '80%'}}></div>
            <div className="w-1 bg-current rounded-full animate-pulse" style={{height: '100%'}}></div>
            <div className="w-1 bg-current rounded-full animate-pulse [animation-delay:-0.2s]" style={{height: '80%'}}></div>
            <div className="w-1 bg-current rounded-full animate-pulse [animation-delay:-0.4s]" style={{height: '60%'}}></div>
          </div>
        );

      case "gradient":
        return (
          <div className={`${sizeClass} ${className}`}>
            <div className="relative">
              <div className="w-full h-full rounded-full bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 animate-spin bg-[length:200%_200%] animate-[spin_1s_linear_infinite,gradient_2s_ease-in-out_infinite]"></div>
              <div className="absolute inset-1 bg-base-100 rounded-full"></div>
            </div>
          </div>
        );

      default:
        return (
          <div className={`${sizeClass} ${colorClass} ${className}`}>
            <div className="relative">
              <div className="w-full h-full bg-current rounded-full animate-ping opacity-75"></div>
              <div className="absolute inset-0 w-full h-full bg-current rounded-full animate-pulse"></div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      {renderSpinner()}
      {text && (
        <p className={`text-sm ${colorClass} animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;
