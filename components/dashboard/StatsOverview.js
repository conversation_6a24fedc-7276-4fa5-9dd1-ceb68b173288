"use client";

import { useState } from "react";
import { formatNumber } from "@/helpers/formatting";
import apiClient from "@/libs/api";

const StatsOverview = ({ data }) => {
  const [isLoadingPortal, setIsLoadingPortal] = useState(false);

  const openStripePortal = async () => {
    setIsLoadingPortal(true);

    try {
      const response = await apiClient.post("/stripe/create-portal", {
        returnUrl: window.location.origin + "/dashboard"
      });

      // Redirect to Stripe customer portal
      window.location.href = response.url;
    } catch (error) {
      console.error("Failed to open Stripe portal:", error);
      // Error handling is already done by apiClient interceptor
    } finally {
      setIsLoadingPortal(false);
    }
  };

  const stats = [
    {
      title: "API Credits",
      value: formatNumber(data.credits_used || 0),
      total:
        data.max_credits_per_month > 0
          ? formatNumber(data.max_credits_per_month)
          : "Unlimited",
      percentage: formatNumber(
        data.credits_used > 0
          ? (data.credits_used / data.max_credits_per_month) * 100
          : 0,
        4
      ),
      change: "+12%",
      changeType: "positive",
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
    },
    {
      title: "API Calls Today",
      value: formatNumber(data.usage_data.total_requests || 0),
      change: "+8%",
      changeType: "positive",
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    },
    {
      title: "WebSocket Connections",
      value: data.active_websocket_connections,
      total: data.max_websocket_connections,
      percentage: formatNumber(
        data.active_websocket_connections > 0
          ? (data.active_websocket_connections /
              data.max_websocket_connections) *
              100
          : 0,
        2
      ),
      change: "0%",
      changeType: "neutral",
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"
          />
        </svg>
      ),
    },
    {
      title: "Your Current Plan",
      value: data.tier_name,
      change: "",
      changeType: "positive",
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
          />
        </svg>
      ),
      extra: (
        <button
          className="mt-1 text-sm font-medium text-purple-600 dark:text-purple-400 hover:underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          onClick={openStripePortal}
          disabled={isLoadingPortal}
        >
          {isLoadingPortal ? (
            <>
              <span className="loading loading-spinner loading-xs"></span>
              Opening...
            </>
          ) : (
            "Manage Plan"
          )}
        </button>
      ),
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
              {stat.icon}
            </div>
            <div
              className={`text-sm font-medium ${
                stat.changeType === "positive"
                  ? "text-green-600 dark:text-green-400"
                  : stat.changeType === "negative"
                  ? "text-red-600 dark:text-red-400"
                  : "text-gray-600 dark:text-gray-400"
              }`}
            >
              {/* {stat.change} */}
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {stat.title}
            </h3>
            <div className="flex items-baseline space-x-2">
              <span className="text-2xl font-bold text-gray-900 dark:text-white">
                {stat.value}
              </span>
              {stat.total && (
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  / {stat.total}
                </span>
              )}
            </div>

            {stat.percentage && (
              <div className="mt-3">
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                  <span>Usage</span>
                  <span>{stat.percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${stat.percentage}%` }}
                  ></div>
                </div>
              </div>
            )}
            {stat?.extra && <>{stat.extra}</>}
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatsOverview;
