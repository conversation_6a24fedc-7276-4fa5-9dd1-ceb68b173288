/**
 * Test file for Stripe Customer Portal Integration
 * 
 * This test verifies that the StatsOverview component properly integrates
 * with the Stripe customer portal functionality.
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import StatsOverview from '../components/dashboard/StatsOverview';
import apiClient from '../libs/api';

// Mock the API client
jest.mock('../libs/api');

// Mock window.location
delete window.location;
window.location = { href: '', origin: 'http://localhost:3000' };

// Mock data for the component
const mockData = {
  credits_used: 150,
  max_credits_per_month: 1000,
  usage_data: {
    total_requests: 45
  },
  active_websocket_connections: 2,
  max_websocket_connections: 10,
  tier_name: 'Premium'
};

describe('Stripe Customer Portal Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    window.location.href = '';
  });

  test('renders Manage Plan button', () => {
    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    expect(manageButton).toBeInTheDocument();
    expect(manageButton).not.toBeDisabled();
  });

  test('shows loading state when opening portal', async () => {
    // Mock API response
    apiClient.post.mockImplementation(() => new Promise(resolve => {
      setTimeout(() => resolve({ url: 'https://billing.stripe.com/session/test' }), 100);
    }));

    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    fireEvent.click(manageButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText('Opening...')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeDisabled();
    });
  });

  test('calls API with correct parameters', async () => {
    // Mock successful API response
    apiClient.post.mockResolvedValue({ url: 'https://billing.stripe.com/session/test' });

    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    fireEvent.click(manageButton);

    await waitFor(() => {
      expect(apiClient.post).toHaveBeenCalledWith('/stripe/create-portal', {
        returnUrl: 'http://localhost:3000/dashboard'
      });
    });
  });

  test('redirects to Stripe portal on success', async () => {
    const mockPortalUrl = 'https://billing.stripe.com/session/test123';
    apiClient.post.mockResolvedValue({ url: mockPortalUrl });

    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    fireEvent.click(manageButton);

    await waitFor(() => {
      expect(window.location.href).toBe(mockPortalUrl);
    });
  });

  test('handles API errors gracefully', async () => {
    // Mock API error
    const mockError = new Error('Failed to create portal');
    apiClient.post.mockRejectedValue(mockError);

    // Mock console.error to avoid test output noise
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    fireEvent.click(manageButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to open Stripe portal:', mockError);
      // Button should be enabled again after error
      expect(screen.getByText('Manage Plan')).toBeInTheDocument();
      expect(screen.getByRole('button')).not.toBeDisabled();
    });

    consoleSpy.mockRestore();
  });

  test('button is properly styled and accessible', () => {
    render(<StatsOverview data={mockData} />);
    
    const manageButton = screen.getByText('Manage Plan');
    
    // Check accessibility
    expect(manageButton).toHaveAttribute('type', 'button');
    
    // Check styling classes
    expect(manageButton).toHaveClass('text-purple-600');
    expect(manageButton).toHaveClass('dark:text-purple-400');
    expect(manageButton).toHaveClass('hover:underline');
  });
});

/**
 * Manual Testing Guide:
 * 
 * 1. Ensure you have a user with a Stripe customer ID in the database
 * 2. Login to the dashboard
 * 3. Navigate to the dashboard page
 * 4. Look for the "Your Current Plan" card
 * 5. Click the "Manage Plan" button
 * 6. Verify:
 *    - Button shows loading state
 *    - User is redirected to Stripe customer portal
 *    - Portal allows subscription management
 *    - Return URL brings user back to dashboard
 * 
 * Error Cases to Test:
 * - User without Stripe customer ID (should show error message)
 * - Unauthenticated user (should redirect to login)
 * - Network errors (should show error toast)
 */
