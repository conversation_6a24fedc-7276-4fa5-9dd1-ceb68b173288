# Manual Stripe Webhook Testing Guide

This guide helps you manually test the refactored Stripe webhook handler using Stripe CLI.

## Prerequisites

1. **Stripe CLI installed and authenticated**
   ```bash
   stripe login
   ```

2. **Development server running**
   ```bash
   pnpm dev
   ```

3. **Database accessible** (PostgreSQL and Redis running)

## Testing Steps

### Step 1: Start Webhook Forwarding

Open a new terminal and run:
```bash
pnpm stripe-webhook
```

This will start forwarding Stripe webhook events to your local development server.

### Step 2: Test Invoice Paid Event (Main Functionality)

In another terminal, trigger the invoice paid event:

```bash
# Test 1: Basic invoice payment (uses random price ID)
stripe trigger invoice.payment_succeeded

# Test 2: Invoice payment with your actual Basic plan price ID
stripe trigger invoice.payment_succeeded --add subscription:price=price_1RYHd0CxyvcNUSW35tQ6qPik

# Test 3: Invoice payment with your actual Premium plan price ID
stripe trigger invoice.payment_succeeded --add subscription:price=price_1RYHeUCxyvcNUSW3kv2TKS2w

# Test 4: Invoice payment with your actual Enterprise plan price ID
stripe trigger invoice.payment_succeeded --add subscription:price=price_1RYHf9CxyvcNUSW3NIQnfxbm
```

**Expected Results:**
- New user created in database (if doesn't exist)
- API key generated for new user
- User granted access (`hasAccess: true`)
- Cache updated
- Success logs in console

### Step 3: Test Subscription Deletion (API Access Revocation)

```bash
stripe trigger customer.subscription.deleted
```

**Expected Results:**
- Revokes access in main database (`hasAccess: false`)
- Sets API tier to 1 (free tier) and credits to 0
- Sets `is_active: false` in API database
- Updates cache appropriately
- Success logs for revocation

### Step 4: Test Checkout Session Completed (Simplified)

```bash
stripe trigger checkout.session.completed
```

**Expected Results:**
- Only logs the checkout session ID
- No database operations
- Quick completion

### Step 5: Test Error Scenarios

```bash
# Test with invalid price ID
stripe trigger invoice.payment_succeeded --override lines.data.0.price.id=price_invalid_test

# Test with missing customer
stripe trigger invoice.payment_succeeded --override customer=cus_invalid_test
```

**Expected Results:**
- Graceful error handling
- Appropriate error logs
- No crashes or exceptions

## Verification Checklist

### ✅ Invoice Paid Event
- [ ] Creates new user when customer doesn't exist
- [ ] Updates existing user for renewals
- [ ] Creates API key for new customers
- [ ] Updates API access for existing customers
- [ ] Updates all relevant cache keys
- [ ] Logs success messages with user ID and plan
- [ ] Handles errors gracefully

### ✅ Subscription Deleted Event
- [ ] Revokes access in main database (`hasAccess: false`)
- [ ] Sets API tier to 1 (free tier)
- [ ] Sets credits to 0 in API database
- [ ] Sets `is_active: false` in API database
- [ ] Updates cache appropriately
- [ ] Logs revocation success messages

### ✅ Checkout Session Completed Event
- [ ] Only logs the event
- [ ] No database operations performed
- [ ] Completes without errors

### ✅ Error Handling
- [ ] Invalid price ID handled gracefully
- [ ] Missing customer handled gracefully
- [ ] Appropriate error messages logged
- [ ] No application crashes

## Log Messages to Look For

### Success Messages
```
Checkout session completed: cs_test_xxx
Created new user for invoice payment: user_xxx
Created API access for new user: user_xxx
Updated API access for existing user: user_xxx
Successfully processed invoice payment for user: user_xxx, plan: Basic
Revoked API access for user: user_xxx (subscription cancelled)
Successfully revoked access for user: user_xxx due to subscription cancellation
```

### Error Messages
```
No plan found for priceId: price_xxx
No user found for customerId: cus_xxx
stripe error: [error message] | EVENT TYPE: invoice.paid
```

## Database Verification

After testing, you can verify the changes in your database:

### Check User Creation/Updates
```sql
-- Check if users were created/updated
SELECT id, email, "customerId", "priceId", "hasAccess", "apiKey", "createdAt", "updatedAt" 
FROM users 
ORDER BY "updatedAt" DESC 
LIMIT 5;
```

### Check API Database
```sql
-- Check if API access was created (in your API database)
SELECT id, email, tier_id, credits_remaining, is_active, created_at 
FROM users 
ORDER BY created_at DESC 
LIMIT 5;
```

## Troubleshooting

### Common Issues

1. **Webhook not receiving events**
   - Ensure `pnpm dev` is running on port 3000
   - Check if `stripe listen` is forwarding to correct URL
   - Verify webhook endpoint is accessible

2. **Database connection errors**
   - Ensure PostgreSQL is running
   - Check environment variables in `.env`
   - Verify database credentials

3. **Redis connection errors**
   - Ensure Redis is running
   - Check Redis connection string in `.env`

4. **Plan not found errors**
   - Verify price IDs in `config.js` match your Stripe dashboard
   - Use correct price IDs when triggering events

### Debug Mode

To see more detailed logs, you can temporarily add debug logging to the webhook handler:

```javascript
// Add this at the beginning of the invoice.paid case
console.log('Invoice paid event data:', JSON.stringify(data.object, null, 2));
```

## Benefits Verification

After testing, you should observe:

1. **Unified Processing**: Both new payments and renewals go through the same code path
2. **No Duplicates**: No duplicate database operations when both events fire
3. **Consistent API Management**: API keys handled consistently for all payment types
4. **Better Logging**: Clear success and error messages for debugging
5. **Simplified Checkout Handler**: Minimal logic in checkout.session.completed

## Next Steps

Once manual testing is complete:

1. Test with real Stripe events in development
2. Monitor webhook performance and error rates
3. Consider adding webhook event deduplication if needed
4. Update monitoring and alerting for the new webhook structure
