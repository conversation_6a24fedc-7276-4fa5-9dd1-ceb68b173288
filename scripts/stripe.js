console.clear();
import dotencv from "dotenv";
dotencv.config();
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-02-24.acacia",
});

(async () => {
  try {
    // get stripe customer
    const customer = await stripe.customers.retrieve("cus_SSsyaa7FxI9wRO");
    console.log(JSON.stringify(customer, null, 2));
    // get all active subscriptions for this customer
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      status: "active",
    });
    console.log(JSON.stringify(subscriptions, null, 2));

    // get the price of the subscription
    const price = await stripe.prices.retrieve(subscriptions.data[0].plan.id);
    console.log(JSON.stringify(price, null, 2));

    // get the product of the price
    const product = await stripe.products.retrieve(price.product);
    console.log(JSON.stringify(product, null, 2));

    // get the features of the product
    // GET /v1/entitlements/features
    // const entitlements = await stripe.entitlements.activeEntitlements.list({
    //   customer: customer.id,
    // });
    console.log(JSON.stringify(Object.keys(stripe), null, 2));
  } catch (e) {
    console.error(e);
  }
})();
